package com.thisapp.base

import android.content.Context
import android.util.Log
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.security.MessageDigest
import java.util.concurrent.TimeUnit
import kotlin.concurrent.thread

/**
 * 资源管理器，负责管理本地和远程资源
 */
class ResourceManager(private val context: Context) {
    
    private val TAG = "ResourceManager"
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()
    
    // 本地资源根目录
    private val localResourceDir: File by lazy {
        File(context.filesDir, "web_resources").apply {
            if (!exists()) mkdirs()
        }
    }
    
    // 版本信息文件
    private val versionFile: File by lazy {
        File(localResourceDir, "version.json")
    }
    
    /**
     * 检查远程资源是否有更新
     * @param remoteVersionUrl 远程版本信息URL
     * @return 是否有更新
     */
    fun checkForUpdates(remoteVersionUrl: String): Boolean {
        try {
            // 获取远程版本信息
            val remoteVersionJson = fetchRemoteContent(remoteVersionUrl)

            // 如果远程版本信息获取失败，返回false表示不需要更新（使用本地资源或直接加载远程）
            if (remoteVersionJson == null) {
                Log.w(TAG, "Failed to fetch remote version info, will use fallback strategy")
                return false
            }

            // 如果本地版本文件不存在，则需要更新
            if (!versionFile.exists()) {
                return true
            }

            // 比较本地和远程版本
            val localVersionJson = versionFile.readText()
            return localVersionJson != remoteVersionJson
        } catch (e: Exception) {
            Log.e(TAG, "Error checking for updates", e)
            return false
        }
    }
    
    /**
     * 下载远程资源到本地
     * @param remoteBaseUrl 远程资源基础URL
     * @param remoteVersionUrl 远程版本信息URL
     * @param callback 下载完成回调
     */
    fun downloadResources(remoteBaseUrl: String, remoteVersionUrl: String, callback: (Boolean) -> Unit) {
        thread {
            try {
                // 获取远程版本信息
                val remoteVersionJson = fetchRemoteContent(remoteVersionUrl)

                if (remoteVersionJson == null) {
                    Log.w(TAG, "Failed to fetch remote version info from: $remoteVersionUrl")
                    Log.i(TAG, "Using fallback strategy: try to download common game files")

                    // 降级策略：尝试下载常见的游戏文件
                    val success = downloadCommonGameFiles(remoteBaseUrl)
                    callback(success)
                    return@thread
                }

                // 解析版本信息中的文件列表
                val fileList = parseFileList(remoteVersionJson)

                if (fileList.isEmpty()) {
                    Log.w(TAG, "No files found in version info or failed to parse, using fallback strategy")
                    // 如果解析失败或文件列表为空，使用降级策略
                    val success = downloadCommonGameFiles(remoteBaseUrl)
                    callback(success)
                    return@thread
                }

                // 下载所有文件
                var success = true
                for (filePath in fileList) {
                    val remoteFileUrl = "$remoteBaseUrl/$filePath"
                    val localFile = File(localResourceDir, filePath)

                    // 确保目录存在
                    localFile.parentFile?.mkdirs()

                    // 下载文件
                    if (!downloadFile(remoteFileUrl, localFile)) {
                        success = false
                        break
                    }
                }

                // 如果所有文件下载成功，保存版本信息
                if (success) {
                    versionFile.writeText(remoteVersionJson)
                }

                callback(success)
            } catch (e: Exception) {
                Log.e(TAG, "Error downloading resources", e)
                callback(false)
            }
        }
    }
    
    /**
     * 获取本地资源路径
     * @return 本地资源路径
     */
    fun getLocalResourcePath(): String {
        return localResourceDir.absolutePath
    }
    
    /**
     * 获取本地资源入口文件路径
     * @param entryFileName 入口文件名，默认为index.html
     * @return 入口文件路径
     */
    fun getLocalEntryFilePath(entryFileName: String = "index.html"): String {
        return File(localResourceDir, entryFileName).absolutePath
    }
    
    /**
     * 检查本地资源是否存在
     * @param entryFileName 入口文件名，默认为index.html
     * @return 本地资源是否存在
     */
    fun isLocalResourceAvailable(entryFileName: String = "index.html"): Boolean {
        return File(localResourceDir, entryFileName).exists() && versionFile.exists()
    }
    
    /**
     * 清除本地资源
     */
    fun clearLocalResources() {
        localResourceDir.deleteRecursively()
        localResourceDir.mkdirs()
    }

    /**
     * 检查远程基础URL是否可访问
     * @param remoteBaseUrl 远程基础URL
     * @return 是否可访问
     */
    fun isRemoteBaseUrlAccessible(remoteBaseUrl: String): Boolean {
        return try {
            Log.d(TAG, "Checking remote base URL accessibility: $remoteBaseUrl")
            val request = Request.Builder()
                .url(remoteBaseUrl)
                .head() // 使用HEAD请求减少数据传输
                .build()

            val response = okHttpClient.newCall(request).execute()
            val isAccessible = response.isSuccessful
            response.close()

            Log.d(TAG, "Remote base URL accessible: $isAccessible")
            isAccessible
        } catch (e: Exception) {
            Log.w(TAG, "Remote base URL not accessible: ${e.message}")
            false
        }
    }
    
    /**
     * 获取远程内容
     * @param url 远程URL
     * @param retryCount 重试次数
     * @return 内容字符串
     */
    private fun fetchRemoteContent(url: String, retryCount: Int = 2): String? {
        var lastException: Exception? = null

        repeat(retryCount + 1) { attempt ->
            try {
                Log.d(TAG, "Fetching remote content from: $url (attempt ${attempt + 1}/${retryCount + 1})")

                val request = Request.Builder()
                    .url(url)
                    .addHeader("User-Agent", "CocosGameLoader/1.0")
                    .build()

                val response = okHttpClient.newCall(request).execute()

                if (!response.isSuccessful) {
                    Log.w(TAG, "HTTP request failed with code: ${response.code}, message: ${response.message}")
                    response.close()
                    return@repeat
                }

                val content = response.body?.string()
                response.close()

                if (content != null) {
                    Log.d(TAG, "Successfully fetched content (${content.length} characters)")
                    return content
                } else {
                    Log.w(TAG, "Response body is null")
                }

            } catch (e: Exception) {
                lastException = e
                Log.w(TAG, "Attempt ${attempt + 1} failed: ${e.message}")

                // 如果不是最后一次尝试，等待一段时间再重试
                if (attempt < retryCount) {
                    try {
                        Thread.sleep(1000L * (attempt + 1)) // 递增等待时间
                    } catch (ie: InterruptedException) {
                        Thread.currentThread().interrupt()
                        break
                    }
                }
            }
        }

        Log.e(TAG, "Failed to fetch remote content after ${retryCount + 1} attempts", lastException)
        return null
    }
    
    /**
     * 下载文件
     * @param url 远程URL
     * @param destFile 目标文件
     * @return 是否下载成功
     */
    private fun downloadFile(url: String, destFile: File): Boolean {
        var response: Response? = null
        try {
            val request = Request.Builder().url(url).build()
            response = okHttpClient.newCall(request).execute()
            if (!response.isSuccessful) {
                return false
            }
            
            val body = response.body ?: return false
            val inputStream = body.byteStream()
            val outputStream = FileOutputStream(destFile)
            
            val buffer = ByteArray(4096)
            var bytesRead: Int
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                outputStream.write(buffer, 0, bytesRead)
            }
            
            outputStream.close()
            inputStream.close()
            return true
        } catch (e: IOException) {
            Log.e(TAG, "Error downloading file", e)
            return false
        } finally {
            response?.close()
        }
    }
    
    /**
     * 降级策略：下载常见的游戏文件
     * @param remoteBaseUrl 远程资源基础URL
     * @return 是否下载成功
     */
    private fun downloadCommonGameFiles(remoteBaseUrl: String): Boolean {
        val commonFiles = listOf(
            "index.html",
            "main.js",
            "game.js",
            "cocos2d-js.js",
            "project.json",
            "src/main.js",
            "res/",
            "assets/"
        )

        var successCount = 0
        for (filePath in commonFiles) {
            try {
                val remoteFileUrl = "$remoteBaseUrl/$filePath"
                val localFile = File(localResourceDir, filePath)

                // 确保目录存在
                localFile.parentFile?.mkdirs()

                // 尝试下载文件
                if (downloadFile(remoteFileUrl, localFile)) {
                    successCount++
                    Log.d(TAG, "Successfully downloaded: $filePath")
                } else {
                    Log.w(TAG, "Failed to download: $filePath (this may be normal if file doesn't exist)")
                }
            } catch (e: Exception) {
                Log.w(TAG, "Error downloading $filePath: ${e.message}")
            }
        }

        // 如果至少下载了一个文件，认为是成功的
        val success = successCount > 0
        Log.i(TAG, "Fallback download completed: $successCount/$commonFiles.size files downloaded")
        return success
    }

    /**
     * 解析版本信息中的文件列表
     * @param versionJson 版本信息JSON
     * @return 文件路径列表
     */
    private fun parseFileList(versionJson: String): List<String> {
        try {
            // 首先尝试检测是否为有效的JSON格式
            if (!isValidJson(versionJson)) {
                Log.w(TAG, "Remote version content is not valid JSON format")
                return emptyList()
            }

            // 简单实现，实际项目中应该使用JSON解析库
            // 这里假设版本信息JSON格式为：{"files":["file1.html","file2.js",...]}
            val fileListRegex = "\"files\":\\s*\\[(.*?)\\]".toRegex()
            val fileRegex = "\"(.*?)\"".toRegex()

            val fileListMatch = fileListRegex.find(versionJson)
            val fileListString = fileListMatch?.groupValues?.get(1) ?: return emptyList()

            return fileRegex.findAll(fileListString)
                .map { it.groupValues[1] }
                .toList()
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing file list from version info", e)
            return emptyList()
        }
    }

    /**
     * 检查字符串是否为有效的JSON格式
     * @param content 要检查的内容
     * @return 是否为有效JSON
     */
    private fun isValidJson(content: String): Boolean {
        val trimmed = content.trim()
        return (trimmed.startsWith("{") && trimmed.endsWith("}")) ||
               (trimmed.startsWith("[") && trimmed.endsWith("]"))
    }
    
    /**
     * 计算文件的MD5哈希值
     * @param file 文件
     * @return MD5哈希值
     */
    private fun calculateMD5(file: File): String {
        val md = MessageDigest.getInstance("MD5")
        val buffer = ByteArray(8192)
        val inputStream = file.inputStream()
        
        var bytesRead: Int
        while (inputStream.read(buffer).also { bytesRead = it } != -1) {
            md.update(buffer, 0, bytesRead)
        }
        
        inputStream.close()
        
        val md5Bytes = md.digest()
        return md5Bytes.joinToString("") { "%02x".format(it) }
    }
}