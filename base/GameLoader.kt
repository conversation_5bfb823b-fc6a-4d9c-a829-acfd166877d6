package com.thisapp.base
import android.content.Context
import android.util.Log
import com.thisapp.base.GameWebView

class GameLoader (private val context: Context,
                  private val webView: GameWebView,
                  private val resourceManager: ResourceManager) {
    private val TAG = "CocosGameLoader"

    // 远程资源基础URL
    private var remoteBaseUrl: String = ""

    // 远程版本信息URL
    private var remoteVersionUrl: String = ""

    // 游戏入口文件名
    private var entryFileName: String = "index.html"

    /**
     * 初始化游戏加载器
     * @param remoteBaseUrl 远程资源基础URL
     * @param remoteVersionUrl 远程版本信息URL
     * @param entryFileName 游戏入口文件名，默认为index.html
     */
    fun init(
        remoteBaseUrl: String,
        remoteVersionUrl: String,
        entryFileName: String = "index.html"
    ) {
        this.remoteBaseUrl = remoteBaseUrl
        this.remoteVersionUrl = remoteVersionUrl
        this.entryFileName = entryFileName
    }

    /**
     * 加载游戏
     * @param forceRemote 是否强制加载远程资源
     * @param callback 加载完成回调
     */
    fun loadGame(forceRemote: Boolean = false, callback: (Boolean) -> Unit) {
        Log.d(TAG, "Loading game - forceRemote: $forceRemote, remoteBaseUrl: $remoteBaseUrl")

        // 检查本地资源是否可用
        val isLocalAvailable = resourceManager.isLocalResourceAvailable(entryFileName)
        Log.d(TAG, "Local resource available: $isLocalAvailable")

        if (isLocalAvailable && !forceRemote) {
            // 检查远程资源是否有更新
            Log.d(TAG, "Checking for updates from: $remoteVersionUrl")
            val hasUpdate = resourceManager.checkForUpdates(remoteVersionUrl)
            Log.d(TAG, "Has update: $hasUpdate")

            if (hasUpdate) {
                // 有更新，下载远程资源
                Log.i(TAG, "Update available, downloading remote resources")
                downloadAndLoadRemoteGame(callback)
            } else {
                // 没有更新，直接加载本地资源
                Log.i(TAG, "No update needed, loading local resources")
                loadLocalGame(callback)
            }
        } else {
            // 本地资源不可用或强制加载远程资源
            Log.i(TAG, "Loading remote resources (local unavailable or forced)")
            downloadAndLoadRemoteGame(callback)
        }
    }

    /**
     * 加载本地游戏
     * @param callback 加载完成回调
     */
    private fun loadLocalGame(callback: (Boolean) -> Unit) {
        try {
            val localEntryPath = resourceManager.getLocalEntryFilePath(entryFileName)
            webView.loadLocalHtml(localEntryPath)
            callback(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error loading local game", e)
            callback(false)
        }
    }

    /**
     * 下载并加载远程游戏
     * @param callback 加载完成回调
     */
    private fun downloadAndLoadRemoteGame(callback: (Boolean) -> Unit) {
        Log.d(TAG, "Starting remote game loading process")

        // 首先检查远程基础URL是否可访问
        if (!resourceManager.isRemoteBaseUrlAccessible(remoteBaseUrl)) {
            Log.e(TAG, "Remote base URL is not accessible: $remoteBaseUrl")
            tryLoadLocalAsFallback(callback)
            return
        }

        // 先尝试直接加载远程游戏
        try {
            Log.d(TAG, "Loading remote URL: $remoteBaseUrl")
            webView.loadRemoteUrl(remoteBaseUrl)

            // 同时下载远程资源到本地（用于缓存）
            resourceManager.downloadResources(remoteBaseUrl, remoteVersionUrl) { downloadSuccess ->
                if (downloadSuccess) {
                    Log.d(TAG, "Remote resources downloaded and cached successfully")
                } else {
                    Log.w(TAG, "Failed to cache remote resources, but remote loading may still work")
                }
                // 无论下载是否成功，都认为加载成功，因为已经直接从远程加载了
                callback(true)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading remote game", e)
            // 如果远程加载失败，尝试加载本地资源作为降级
            tryLoadLocalAsFallback(callback)
        }
    }

    /**
     * 降级策略：尝试加载本地资源
     * @param callback 加载完成回调
     */
    private fun tryLoadLocalAsFallback(callback: (Boolean) -> Unit) {
        Log.w(TAG, "Remote loading failed, trying local fallback")

        if (resourceManager.isLocalResourceAvailable(entryFileName)) {
            Log.i(TAG, "Local resources available, using as fallback")
            loadLocalGame(callback)
        } else {
            Log.e(TAG, "No local resources available for fallback")
            callback(false)
        }
    }

    /**
     * 重新加载游戏
     */
    fun reloadGame() {
        webView.reload()
    }

    /**
     * 清除游戏缓存
     */
    fun clearGameCache() {
        webView.clearWebViewCache()
        resourceManager.clearLocalResources()
    }

    /**
     * 执行游戏中的JavaScript代码
     * @param script JavaScript代码
     */
    fun executeGameScript(script: String) {
        webView.executeJavaScript(script)
    }
}